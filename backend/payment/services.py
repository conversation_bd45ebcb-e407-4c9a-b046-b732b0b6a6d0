"""
Payment services for handling business logic and external integrations
"""
import logging
from typing import Optional, Tuple
from decimal import Decimal
from django.db import transaction
from django.utils import timezone
from django.conf import settings

from .models import Payment
from main.models import Subscription, User

logger = logging.getLogger(__name__)


class PaymentService:
    """Service class for handling payment operations"""
    
    @staticmethod
    @transaction.atomic
    def create_payment_record(
        user: User,
        amount: Decimal,
        currency: str,
        membership_tier: str
    ) -> Payment:
        """
        Create a new payment record with proper validation
        
        Args:
            user: User making the payment
            amount: Payment amount
            currency: Currency code
            membership_tier: Target membership tier
            
        Returns:
            Payment: Created payment record
            
        Raises:
            ValueError: If validation fails
        """
        try:
            # Validate amount
            if amount <= 0:
                raise ValueError("Payment amount must be positive")
            
            # Validate currency
            valid_currencies = ['CNY', 'HKD', 'USD']
            if currency not in valid_currencies:
                raise ValueError(f"Currency must be one of {valid_currencies}")
            
            # Validate membership tier
            valid_tiers = ['free', 'premium', 'plus']
            if membership_tier not in valid_tiers:
                raise ValueError(f"Membership tier must be one of {valid_tiers}")
            
            # Create payment record
            payment = Payment.objects.create(
                user=user,
                amount=amount,
                currency=currency,
                membership_tier=membership_tier,
                status='created'
            )
            
            logger.info(f"Created payment record {payment.id} for user {user.id}")
            return payment
            
        except Exception as e:
            logger.error(f"Failed to create payment record for user {user.id}: {str(e)}")
            raise
    
    @staticmethod
    @transaction.atomic
    def update_payment_status(
        payment_id: str,
        status: str,
        metadata: Optional[dict] = None
    ) -> Payment:
        """
        Update payment status with proper validation
        
        Args:
            payment_id: Payment record ID
            status: New status
            metadata: Additional metadata to store
            
        Returns:
            Payment: Updated payment record
            
        Raises:
            Payment.DoesNotExist: If payment not found
            ValueError: If status is invalid
        """
        try:
            payment = Payment.objects.select_for_update().get(id=payment_id)
            
            # Validate status transition
            valid_statuses = ['created', 'pending', 'processing', 'success', 'failed', 'cancelled', 'refunded']
            if status not in valid_statuses:
                raise ValueError(f"Invalid status: {status}")
            
            # Check if status transition is allowed
            if not PaymentService._is_valid_status_transition(payment.status, status):
                raise ValueError(f"Invalid status transition from {payment.status} to {status}")
            
            # Update payment
            payment.status = status
            if status == 'success' and not payment.paid_at:
                payment.paid_at = timezone.now()
            
            if metadata:
                payment.metadata.update(metadata)
            
            payment.save()
            
            logger.info(f"Updated payment {payment_id} status to {status}")
            return payment
            
        except Payment.DoesNotExist:
            logger.error(f"Payment {payment_id} not found")
            raise
        except Exception as e:
            logger.error(f"Failed to update payment {payment_id} status: {str(e)}")
            raise
    
    @staticmethod
    def _is_valid_status_transition(current_status: str, new_status: str) -> bool:
        """
        Check if status transition is valid
        
        Args:
            current_status: Current payment status
            new_status: Target status
            
        Returns:
            bool: True if transition is valid
        """
        # Define valid transitions
        valid_transitions = {
            'created': ['pending', 'processing', 'success', 'failed', 'cancelled'],
            'pending': ['processing', 'success', 'failed', 'cancelled'],
            'processing': ['success', 'failed'],
            'success': ['refunded'],  # Only allow refund from success
            'failed': [],  # Terminal state
            'cancelled': [],  # Terminal state
            'refunded': []  # Terminal state
        }
        
        return new_status in valid_transitions.get(current_status, [])
    
    @staticmethod
    @transaction.atomic
    def update_user_subscription(payment: Payment) -> Subscription:
        """
        Update user subscription based on successful payment
        
        Args:
            payment: Successful payment record
            
        Returns:
            Subscription: Updated subscription
            
        Raises:
            ValueError: If payment is not successful
        """
        try:
            if not payment.is_successful:
                raise ValueError("Payment must be successful to update subscription")
            
            # Get or create subscription
            subscription, created = Subscription.objects.get_or_create(
                user=payment.user,
                defaults={
                    'subscription_type': 'free',
                    'is_active': True
                }
            )
            
            # Calculate subscription dates
            start_date, end_date = payment.calculate_subscription_dates()
            
            # Update subscription
            old_type = subscription.subscription_type
            subscription.subscription_type = payment.membership_tier
            subscription.is_active = True
            
            if start_date:
                subscription.start_date = start_date
                payment.subscription_start_date = start_date
            
            if end_date:
                subscription.end_date = end_date
                payment.subscription_end_date = end_date
            
            subscription.save()
            payment.save()
            
            logger.info(
                f"Updated subscription for user {payment.user.id} "
                f"from {old_type} to {payment.membership_tier}"
            )
            
            return subscription
            
        except Exception as e:
            logger.error(f"Failed to update subscription for payment {payment.id}: {str(e)}")
            raise


class PaymentValidationService:
    """Service for payment validation logic"""
    
    @staticmethod
    def validate_payment_amount(amount: float, membership_tier: str) -> bool:
        """
        Validate payment amount against membership tier
        
        Args:
            amount: Payment amount
            membership_tier: Target membership tier
            
        Returns:
            bool: True if amount is valid for tier
        """
        tier_amounts = {
            'free': 0.0,
            'premium': 29.0,
            'plus': 99.0,
        }
        
        expected_amount = tier_amounts.get(membership_tier)
        if expected_amount is None:
            return False
        
        # Allow some tolerance for currency conversion
        tolerance = 0.01
        return abs(amount - expected_amount) <= tolerance
    
    @staticmethod
    def validate_user_eligibility(user: User, membership_tier: str) -> Tuple[bool, str]:
        """
        Validate if user is eligible for the membership tier
        
        Args:
            user: User requesting the subscription
            membership_tier: Target membership tier
            
        Returns:
            Tuple[bool, str]: (is_eligible, reason)
        """
        try:
            subscription = user.subscription
            
            # Check if user already has this tier or higher
            tier_hierarchy = {'free': 0, 'premium': 1, 'plus': 2}
            current_level = tier_hierarchy.get(subscription.subscription_type, 0)
            target_level = tier_hierarchy.get(membership_tier, 0)
            
            if current_level >= target_level and subscription.is_active:
                return False, f"User already has {subscription.subscription_type} subscription"
            
            # Check for pending payments
            pending_payments = Payment.objects.filter(
                user=user,
                status__in=['created', 'pending', 'processing']
            ).exists()
            
            if pending_payments:
                return False, "User has pending payments"
            
            return True, "User is eligible"
            
        except Subscription.DoesNotExist:
            # User has no subscription, eligible for any tier
            return True, "User is eligible"
        except Exception as e:
            logger.error(f"Error validating user {user.id} eligibility: {str(e)}")
            return False, "Validation error"
